# Database Configuration
DATABASE_URL="postgresql://user:password@localhost:5432/gistmind"

# Firebase Configuration (Base64 encoded service account JSON)
GOOGLE_APPLICATION_CREDENTIALS_JSON="your_base64_encoded_service_account_json_here"

# Service URLs
AUTH_SERVICE_URL="http://localhost:3002"
CONTENT_SERVICE_URL="http://localhost:3003"
FRONTEND_URL="http://localhost:3001"

# API Gateway
PORT=3000

# Auth Service
AUTH_SERVICE_PORT=3002

# Content Service  
CONTENT_SERVICE_PORT=3003

# Redis (for caching)
REDIS_URL="redis://localhost:6379"

# RabbitMQ (for message queuing)
RABBIT_MQ_URL="amqp://user:password@localhost:5672"
