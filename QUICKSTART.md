# GistMind Authentication - Quick Start Guide

## 🚀 Get Started in 5 Minutes

### 1. Run Setup Script
```bash
./scripts/setup-auth.sh
```

### 2. Configure Environment
Update `.env` with your database URL:
```env
DATABASE_URL="postgresql://user:password@localhost:5432/gistmind"
```

Update `apps/web/.env.local` with your Firebase config:
```env
NEXT_PUBLIC_FIREBASE_API_KEY="your_api_key"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="your_project.firebaseapp.com"
# ... other Firebase config
```

### 3. Setup Database
```bash
cd packages/db
npx prisma db push
cd ../..
```

### 4. Start Services
```bash
npm run dev
```

### 5. Test Authentication
1. Visit: http://localhost:3001/login
2. Sign in with email/password
3. Check dashboard: http://localhost:3001/dashboard

## 🔧 Service URLs
- **Frontend**: http://localhost:3001
- **API Gateway**: http://localhost:3000
- **Auth Service**: http://localhost:3002

## 📋 What's Included

✅ **Complete Authentication System**
- Firebase Auth integration
- User database synchronization
- JWT token verification
- Secure API gateway

✅ **Microservices Architecture**
- API Gateway (port 3000)
- Auth Service (port 3002)
- Frontend (port 3001)

✅ **Database Integration**
- PostgreSQL with Prisma
- User profile management
- Automatic user sync

✅ **Security Features**
- Token verification
- CORS protection
- Request validation
- Error handling

## 🎯 Key Features

### Frontend Integration
- Automatic user sync on login
- Backend user profile access
- Real-time authentication state
- Error handling with toast notifications

### API Gateway
- Firebase token verification
- Request routing to microservices
- User context injection
- Health monitoring

### Auth Service
- User CRUD operations
- Profile management
- Firebase Admin integration
- Database synchronization

## 🔍 Testing Your Setup

### Health Checks
```bash
# Gateway health
curl http://localhost:3000/health

# Auth service health  
curl http://localhost:3002/health

# Services status
curl http://localhost:3000/api/status
```

### User Authentication Flow
1. **Login**: User signs in via Firebase Auth
2. **Sync**: Frontend automatically syncs user with backend
3. **Access**: User can access protected routes
4. **Profile**: Backend user profile is available in React context

## 📚 Next Steps

1. **Customize User Model**: Update Prisma schema in `packages/db/prisma/schema.prisma`
2. **Add Content Service**: Extend with content management features
3. **Implement Roles**: Add user roles and permissions
4. **Add Caching**: Integrate Redis for performance
5. **Deploy**: Set up production environment

## 🆘 Need Help?

- Check `docs/AUTHENTICATION.md` for detailed documentation
- Review service logs for debugging
- Test API endpoints with provided curl examples
- Verify environment variables are set correctly

## 🎉 You're Ready!

Your GistMind authentication system is now fully functional with:
- ✅ Firebase Auth frontend
- ✅ Secure API gateway
- ✅ User management service
- ✅ Database synchronization
- ✅ Complete user flow

Start building your content management features on top of this solid authentication foundation!
