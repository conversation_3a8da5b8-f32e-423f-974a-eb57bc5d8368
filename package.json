{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "5.8.3"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.18", "workspaces": ["apps/*", "packages/*"]}