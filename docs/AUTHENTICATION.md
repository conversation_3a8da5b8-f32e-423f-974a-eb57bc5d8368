# GistMind Authentication System

This document describes the complete authentication architecture for GistMind, including Firebase Auth integration, microservices, and API gateway.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │  Auth Service   │    │   Database      │
│   (Next.js)     │    │   (Express)     │    │   (Express)     │    │  (PostgreSQL)   │
│                 │    │                 │    │                 │    │                 │
│ Firebase Auth   │◄──►│ Token Verify    │◄──►│ User Management │◄──►│ User Storage    │
│ Login/Register  │    │ Route Proxy     │    │ Profile Sync    │    │ Prisma ORM      │
│ Token Management│    │ CORS Handling   │    │ CRUD Operations │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components

### 1. Frontend (Next.js)
- **Location**: `apps/web/`
- **Port**: 3001
- **Responsibilities**:
  - Firebase Auth UI (login/register)
  - Token management
  - API calls to backend
  - User interface

### 2. API Gateway
- **Location**: `apps/api-gateway/`
- **Port**: 3000
- **Responsibilities**:
  - Firebase token verification
  - Request routing to microservices
  - CORS handling
  - User context injection

### 3. Auth Service
- **Location**: `apps/auth-service/`
- **Port**: 3002
- **Responsibilities**:
  - User profile management
  - Database synchronization
  - CRUD operations for users

### 4. Database
- **Technology**: PostgreSQL + Prisma
- **Location**: `packages/db/`
- **Responsibilities**:
  - User data storage
  - Relational data management

## Authentication Flow

### 1. User Login
```
1. User enters credentials in frontend
2. Frontend calls Firebase Auth
3. Firebase returns ID token
4. Frontend stores token and makes API calls
5. API Gateway verifies token with Firebase Admin
6. Gateway forwards request to Auth Service
7. Auth Service syncs user with database
8. User profile returned to frontend
```

### 2. API Request Flow
```
1. Frontend includes Bearer token in Authorization header
2. API Gateway intercepts request
3. Gateway verifies token with Firebase Admin
4. Gateway extracts user info (uid, email)
5. Gateway adds user headers (x-user-id, x-user-email)
6. Gateway proxies request to appropriate service
7. Service processes request with user context
8. Response returned through gateway to frontend
```

## API Endpoints

### API Gateway (`http://localhost:3000`)
- `GET /health` - Gateway health check
- `GET /api/status` - Services status
- `POST /api/auth/sync` - Sync user with database
- `*    /api/auth/*` - Proxy to Auth Service
- `*    /api/content/*` - Proxy to Content Service
- `*    /api/users/*` - Legacy proxy to Auth Service

### Auth Service (`http://localhost:3002`)
- `GET /health` - Service health check
- `POST /users/sync` - Sync Firebase user to database
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `DELETE /users/profile` - Delete user account
- `POST /auth/verify` - Verify Firebase token (debug)

## Setup Instructions

### 1. Prerequisites
- Node.js 18+
- PostgreSQL database
- Firebase project with Auth enabled

### 2. Installation
```bash
# Run the setup script
chmod +x scripts/setup-auth.sh
./scripts/setup-auth.sh
```

### 3. Configuration

#### Environment Variables
Create `.env` in project root:
```env
DATABASE_URL="postgresql://user:password@localhost:5432/gistmind"
GOOGLE_APPLICATION_CREDENTIALS_JSON="base64_encoded_service_account"
AUTH_SERVICE_URL="http://localhost:3002"
CONTENT_SERVICE_URL="http://localhost:3003"
FRONTEND_URL="http://localhost:3001"
```

Create `apps/web/.env.local`:
```env
NEXT_PUBLIC_FIREBASE_API_KEY="your_api_key"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="your_project.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="your_project_id"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="your_project.appspot.com"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="your_sender_id"
NEXT_PUBLIC_FIREBASE_APP_ID="your_app_id"
NEXT_PUBLIC_API_GATEWAY_URL="http://localhost:3000"
```

### 4. Database Setup
```bash
cd packages/db
npx prisma generate
npx prisma db push
```

### 5. Running Services
```bash
# Start all services
npm run dev

# Or start individually
cd apps/api-gateway && npm run dev    # Port 3000
cd apps/auth-service && npm run dev   # Port 3002
cd apps/web && npm run dev            # Port 3001
```

## Testing the System

### 1. Health Checks
- Gateway: http://localhost:3000/health
- Auth Service: http://localhost:3002/health
- Services Status: http://localhost:3000/api/status

### 2. User Flow Test
1. Visit http://localhost:3001/login
2. Sign in with email/password or OAuth
3. Check dashboard at http://localhost:3001/dashboard
4. Verify user sync in database

### 3. API Testing
```bash
# Get user token from browser dev tools after login
TOKEN="your_firebase_id_token"

# Test user sync
curl -X POST http://localhost:3000/api/auth/sync \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"fullName": "Test User"}'

# Test user profile
curl http://localhost:3000/api/auth/users/profile \
  -H "Authorization: Bearer $TOKEN"
```

## Security Features

- Firebase Admin SDK token verification
- CORS protection
- Request validation with Zod
- User context isolation
- Secure header forwarding
- Error handling and logging

## Troubleshooting

### Common Issues
1. **Token verification fails**: Check Firebase service account configuration
2. **Database connection fails**: Verify DATABASE_URL and PostgreSQL service
3. **CORS errors**: Check FRONTEND_URL in gateway configuration
4. **User sync fails**: Check auth service logs and database connectivity

### Debug Mode
Enable detailed logging by setting `NODE_ENV=development` in service environments.
