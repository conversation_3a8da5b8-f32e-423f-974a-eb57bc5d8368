{"name": "api-gateway", "version": "1.0.0", "description": "API Gateway for GistMind microservices", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["api-gateway", "microservices", "firebase"], "devDependencies": {"@repo/typescript-config": "*", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^22.15.3", "tsx": "^4.19.2", "typescript": "^5.8.2"}, "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "http-proxy-middleware": "^3.0.5", "morgan": "^1.10.1"}}