import express, { Request, Response, NextFunction } from "express";
import { createProxyMiddleware } from "http-proxy-middleware";
import type { ClientRequest } from "http";
import cors from "cors";
import morgan from "morgan";
import admin from "firebase-admin";
import dotenv from "dotenv";

dotenv.config();

// --- Initialize Firebase Admin ---
if (!admin.apps.length) {
  const serviceAccount = JSON.parse(
    Buffer.from(
      process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON!,
      "base64"
    ).toString("ascii")
  );
  admin.initializeApp({ credential: admin.credential.cert(serviceAccount) });
}

// --- Firebase Auth Middleware ---
const firebaseAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(403).json({
      error: "Unauthorized: No token provided",
      code: "NO_TOKEN",
    });
  }

  const idToken = authHeader.split("Bearer ")[1];
  if (!idToken) {
    return res.status(403).json({
      error: "Unauthorized: Invalid token format",
      code: "INVALID_TOKEN_FORMAT",
    });
  }

  try {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    (req as any).user = decodedToken;
    console.log(
      `✅ Authenticated user: ${decodedToken.email} (${decodedToken.uid})`
    );
    next();
  } catch (error) {
    console.error("❌ Error verifying Firebase ID token:", error);
    return res.status(403).json({
      error: "Unauthorized: Invalid or expired token",
      code: "INVALID_TOKEN",
    });
  }
};

const app = express();
const PORT = process.env.PORT || 3001;

// --- Middleware Setup ---
app.use(
  cors({
    origin: [
      process.env.FRONTEND_URL || "http://localhost:3000",
      "http://localhost:3001",
    ],
    credentials: true,
  })
);
app.use(express.json({ limit: "10mb" }));
app.use(morgan("dev"));

// --- Health Check Endpoint ---
app.get("/health", (_req: Request, res: Response) => {
  res.status(200).json({
    status: "healthy",
    service: "api-gateway",
    timestamp: new Date().toISOString(),
  });
});

// --- Proxy Event Handler ---
const onProxyReq = (proxyReq: ClientRequest, req: Request, _res: Response) => {
  const user = (req as any).user;
  if (user) {
    proxyReq.setHeader("x-user-id", user.uid);
    proxyReq.setHeader("x-user-email", user.email || "");
    proxyReq.setHeader("x-user-name", user.name || "");
  }
};

// --- Direct Handling for User Sync ---
app.post(
  "/api/auth/sync",
  firebaseAuthMiddleware,
  async (req: Request, res: Response) => {
    try {
      const user = (req as any).user;
      const authServiceUrl =
        process.env.AUTH_SERVICE_URL || "http://localhost:3002";

      // Manually forward the request to the auth/user service
      const response = await fetch(`${authServiceUrl}/users/sync`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": user.uid,
          "x-user-email": user.email || "",
        },
        body: JSON.stringify(req.body),
      });

      if (!response.ok) {
        throw new Error(
          `Auth service responded with status ${response.status}`
        );
      }

      const data = await response.json();
      res.status(response.status).json(data);
    } catch (error) {
      console.error("❌ Error syncing user:", error);
      res.status(500).json({
        error: "Failed to sync user",
        code: "SYNC_ERROR",
      });
    }
  }
);

// --- Service Proxies ---
app.use(
  "/api/content",
  firebaseAuthMiddleware,
  createProxyMiddleware({
    target: process.env.CONTENT_SERVICE_URL || "http://localhost:3003",
    changeOrigin: true,
    pathRewrite: { "^/api/content": "" },
    on: { proxyReq: onProxyReq },
  })
);

// Catch-all for other /api/auth routes
app.use(
  "/api/auth",
  firebaseAuthMiddleware,
  createProxyMiddleware({
    target: process.env.AUTH_SERVICE_URL || "http://localhost:3002",
    changeOrigin: true,
    pathRewrite: { "^/api/auth": "" },
    on: { proxyReq: onProxyReq },
  })
);

// --- Error Handling ---
app.use((_req: Request, res: Response) => {
  res.status(404).json({
    error: "Route not found on gateway",
    code: "NOT_FOUND",
  });
});

// --- Start Server ---
app.listen(PORT, () => {
  console.log(`🚀 API Gateway running on http://localhost:${PORT}`);
});
