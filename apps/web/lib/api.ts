// lib/api.ts - Frontend API client for GistMind

import { auth } from "./firebase";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_GATEWAY_URL || "http://localhost:3001";

// Types
export interface User {
  id: string;
  email: string;
  fullName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  user?: T;
  data?: T;
  error?: string;
  code?: string;
  details?: any;
}

// Helper function to get auth headers
async function getAuthHeaders(): Promise<Record<string, string>> {
  const user = auth.currentUser;
  if (!user) {
    throw new Error("User not authenticated");
  }

  const idToken = await user.getIdToken();
  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${idToken}`,
  };
}

// Generic API request function
async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  try {
    const headers = await getAuthHeaders();

    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.error || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    return data;
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

// Auth API functions
export const authApi = {
  // Sync user with backend database
  async syncUser(userData?: { fullName?: string }): Promise<ApiResponse<User>> {
    return apiRequest("/api/auth/sync", {
      method: "POST",
      body: JSON.stringify(userData || {}),
    });
  },

  // Get user profile
  async getProfile(): Promise<ApiResponse<User>> {
    return apiRequest("/api/auth/users/profile");
  },

  // Update user profile
  async updateProfile(userData: {
    fullName?: string;
    email?: string;
  }): Promise<ApiResponse<User>> {
    return apiRequest("/api/auth/users/profile", {
      method: "PUT",
      body: JSON.stringify(userData),
    });
  },

  // Delete user account
  async deleteAccount(): Promise<ApiResponse> {
    return apiRequest("/api/auth/users/profile", {
      method: "DELETE",
    });
  },

  // Verify token (for debugging)
  async verifyToken(idToken: string): Promise<ApiResponse> {
    return fetch(`${API_BASE_URL}/api/auth/auth/verify`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ idToken }),
    }).then((res) => res.json());
  },
};

// System API functions
export const systemApi = {
  // Check gateway health
  async getHealth(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/health`);
    return response.json();
  },

  // Check services status
  async getStatus(): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/api/status`);
    return response.json();
  },
};

// Hook for automatic user sync on login
export async function syncUserOnLogin(userData?: {
  fullName?: string;
}): Promise<User | null> {
  try {
    console.log("🔄 Syncing user with backend...");
    const response = await authApi.syncUser(userData);

    if (response.success && response.user) {
      console.log("✅ User synced successfully:", response.user);
      return response.user;
    } else {
      console.error("❌ User sync failed:", response.error);
      return null;
    }
  } catch (error) {
    console.error("❌ Error syncing user:", error);
    return null;
  }
}

// Error handling utility
export function handleApiError(error: any): string {
  if (error.message) {
    return error.message;
  }

  if (typeof error === "string") {
    return error;
  }

  return "An unexpected error occurred";
}
