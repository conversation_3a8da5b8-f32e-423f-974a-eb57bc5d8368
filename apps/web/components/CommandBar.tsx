"use client";

import { useState } from "react";
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Search,
  Plus,
  Home,
  Library,
  Sparkles,
  Tag,
  Folder,
  Settings,
} from "lucide-react";
import { useRouter } from "next/navigation";

interface CommandBarProps {
  isOpen: boolean;
  onClose: () => void;
}

const mockSearchResults = [
  {
    id: 1,
    title: "React Performance Optimization",
    type: "article",
    snippet: "Learn how to optimize React apps with memo, useMemo...",
  },
  {
    id: 2,
    title: "TypeScript Best Practices",
    type: "article",
    snippet: "Essential patterns for writing better TypeScript code...",
  },
  {
    id: 3,
    title: "Design Systems Tutorial",
    type: "video",
    snippet: "Complete guide to building scalable design systems...",
  },
];

const navigationCommands = [
  { title: "Dashboard", icon: Home, action: "/dashboard" },
  { title: "All Content", icon: Library, action: "/content" },
  { title: "My Gists", icon: Sparkles, action: "/gists" },
  { title: "Tags", icon: Tag, action: "/tags" },
  { title: "Settings", icon: Settings, action: "/settings" },
];

export function CommandBar({ isOpen, onClose }: CommandBarProps) {
  const [query, setQuery] = useState("");
  const router = useRouter();

  const handleSelect = (action: string) => {
    if (action.startsWith("/")) {
      router.push(action);
    }

    onClose();
    setQuery("");
  };

  return (
    <CommandDialog open={isOpen} onOpenChange={onClose}>
      <CommandInput
        placeholder="Search content or navigate..."
        value={query}
        onValueChange={setQuery}
      />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>

        {query && (
          <CommandGroup heading="Search Results">
            {mockSearchResults
              .filter(
                (item) =>
                  item.title.toLowerCase().includes(query.toLowerCase()) ||
                  item.snippet.toLowerCase().includes(query.toLowerCase())
              )
              .map((item) => (
                <CommandItem
                  key={item.id}
                  value={item.title}
                  onSelect={() => handleSelect(`/content/${item.id}`)}
                  className="flex flex-col items-start p-3"
                >
                  <div className="flex items-center gap-2 w-full">
                    <Search className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{item.title}</span>
                    <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded ml-auto">
                      {item.type}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1 line-clamp-1">
                    {item.snippet}
                  </p>
                </CommandItem>
              ))}
          </CommandGroup>
        )}

        <CommandGroup heading="Navigation">
          {navigationCommands.map((cmd) => (
            <CommandItem
              key={cmd.title}
              value={cmd.title}
              onSelect={() => handleSelect(cmd.action)}
              className="flex items-center gap-2"
            >
              <cmd.icon className="h-4 w-4" />
              <span>Go to {cmd.title}</span>
            </CommandItem>
          ))}
        </CommandGroup>

        <CommandGroup heading="Actions">
          <CommandItem
            value="add content"
            onSelect={() => handleSelect("add-content")}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add new content</span>
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
}
