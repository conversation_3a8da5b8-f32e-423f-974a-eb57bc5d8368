"use client";

import { useEffect } from "react";
import { useSet<PERSON>tom } from "jotai";
import { authListenerAtom } from "@/app/store/authAtom";

export function AuthInitializer() {
  const startAuthListener = useSetAtom(authListenerAtom);

  useEffect(() => {
    const unsubscribe = startAuthListener();
    return () => unsubscribe();
  }, [startAuthListener]);

  // This component renders nothing
  return null;
}
