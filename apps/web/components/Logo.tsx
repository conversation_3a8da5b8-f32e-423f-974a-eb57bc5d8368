import { cn } from "@/lib/utils";

interface LogoProps {
  className?: string;
  variant?: "full" | "icon";
  size?: "sm" | "md" | "lg";
}

export const Logo = ({
  className,
  variant = "full",
  size = "md",
}: LogoProps) => {
  const sizes = {
    sm: { width: 120, height: 32, iconSize: 32 },
    md: { width: 165, height: 40, iconSize: 40 },
    lg: { width: 220, height: 56, iconSize: 56 },
  };

  const currentSize = sizes[size];

  // You can change this value to increase or decrease the space
  const gap = 12;

  if (variant === "icon") {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={currentSize.iconSize}
        height={currentSize.iconSize}
        viewBox={`0 0 ${currentSize.iconSize} ${currentSize.iconSize}`}
        className={cn("transition-all duration-300", className)}
      >
        <g transform={`scale(${currentSize.iconSize / 40})`}>
          <path
            fill="none"
            stroke="var(--gist-primary)"
            strokeWidth="2"
            d="M20 8a12 12 0 100 24 12 12 0 000-24z"
          />
          <path
            stroke="var(--gist-accent)"
            strokeWidth="1.5"
            strokeLinecap="round"
            d="M4 14h12 M4 20h14 M4 26h12"
          />
          <circle cx="20" cy="20" r="4" fill="var(--gist-primary)" />
          <path
            stroke="var(--gist-accent)"
            strokeWidth="1"
            strokeLinecap="round"
            opacity="0.6"
            d="M16 14l4 6 M16 20l4 0 M16 26l4-6"
          />
        </g>
      </svg>
    );
  }

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={currentSize.width}
      height={currentSize.height}
      viewBox={`0 0 ${currentSize.width} ${currentSize.height}`}
      className={cn("transition-all duration-300", className)}
    >
      <defs>
        <style>
          {`.cls-a { fill: var(--color-foreground); font-family: Inter, sans-serif; }
            .cls-b { font-size: ${
              size === "sm" ? "18px" : size === "lg" ? "32px" : "24px"
            }; font-weight: 600; }
            .cls-c { font-size: ${
              size === "sm" ? "18px" : size === "lg" ? "32px" : "24px"
            }; font-weight: 300; }`}
        </style>
      </defs>

      <g transform={`scale(${currentSize.iconSize / 40})`}>
        <path
          fill="none"
          stroke="var(--gist-primary)"
          strokeWidth="2"
          d="M20 8a12 12 0 100 24 12 12 0 000-24z"
        />
        <path
          stroke="var(--gist-accent)"
          strokeWidth="1.5"
          strokeLinecap="round"
          d="M4 14h12 M4 20h14 M4 26h12"
        />
        <circle cx="20" cy="20" r="4" fill="var(--gist-primary)" />
        <path
          stroke="var(--gist-accent)"
          strokeWidth="1"
          strokeLinecap="round"
          opacity="0.6"
          d="M16 14l4 6 M16 20l4 0 M16 26l4-6"
        />
      </g>

      <text
        id="wordmark"
        className="cls-a"
        // CHANGE: The 'x' attribute now uses the icon's size plus the gap
        x={currentSize.iconSize + gap}
        y={size === "sm" ? "24" : size === "lg" ? "38" : "29"}
      >
        <tspan className="cls-b">Gist</tspan>
        <tspan className="cls-c">Mind</tspan>
      </text>
    </svg>
  );
};
