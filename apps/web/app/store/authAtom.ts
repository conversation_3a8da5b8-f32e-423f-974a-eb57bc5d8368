import { atom } from 'jotai';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { syncUserOnLogin, type User as BackendUser } from '@/lib/api';

// Atom to manage the initial loading state
export const authLoadingAtom = atom(true);

// This atom holds the Firebase user state
export const firebaseUserAtom = atom<User | null>(null);

// This is a "write-only" atom to control the listener.
// It doesn't hold a value itself, it just performs an action.
export const authListenerAtom = atom(
  null, // The first argument (the "read" function) is null because we only write to it.
  (get, set) => {
    // The second argument is the "write" function. It gives us a `set` that can update ANY atom.
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      set(firebase<PERSON>ser<PERSON>tom, user);
      set(authLoadingAtom, false); // This now works correctly.
    });
    return unsubscribe;
  }
);

// This derived async atom for the backend profile remains the same.
export const backendUserAtom = atom<Promise<BackendUser | null>>(async (get) => {
  const firebaseUser = get(firebaseUserAtom);
  if (!firebaseUser) {
    return null;
  }
  try {
    return await syncUserOnLogin({
      fullName: firebaseUser.displayName || undefined,
    });
  } catch (error) {
    console.error("Failed to sync user with backend:", error);
    return null;
  }
});