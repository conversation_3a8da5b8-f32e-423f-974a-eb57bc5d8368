@import "tailwindcss";

/* Declare custom dark variant */
@custom-variant dark (&:where(.dark, .dark *));

@theme {
  /* Core Variables */
  --radius: 0.75rem;
  --color-background: hsl(215 28% 9%);
  --color-foreground: hsl(210 40% 90%);
  --color-card: hsl(217 19% 15%);
  --color-card-foreground: hsl(210 40% 90%);
  --color-popover: hsl(217 19% 15%);
  --color-popover-foreground: hsl(210 40% 90%);
  --color-primary: hsl(238 84% 58%);
  --color-primary-foreground: hsl(210 40% 98%);
  --color-secondary: hsl(217 19% 15%);
  --color-secondary-foreground: hsl(210 40% 90%);
  --color-muted: hsl(217 19% 15%);
  --color-muted-foreground: hsl(215 25% 62%);
  --color-accent: hsl(160 84% 39%);
  --color-accent-foreground: hsl(210 40% 98%);
  --color-destructive: hsl(0 84% 60%);
  --color-destructive-foreground: hsl(210 40% 98%);
  --color-border: hsl(217 19% 25%);
  --color-input: hsl(217 19% 25%);
  --color-ring: hsl(238 84% 58%);

  /* --- MISSING VALUES ADDED BELOW --- */

  /* Gist Mind specific colors */
  --gist-primary: hsl(238 84% 58%);
  --gist-accent: hsl(160 84% 39%);
  --gist-charcoal: hsl(215 28% 9%);
  --gist-gray: hsl(215 25% 62%);
  --gist-light: hsl(210 40% 98%);

  /* Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    hsl(var(--gist-primary)),
    hsl(var(--gist-accent))
  );
  --gradient-subtle: linear-gradient(
    180deg,
    hsl(var(--color-background)),
    hsl(217 19% 12%)
  );

  /* Animations & Shadows */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --shadow-glow: 0 0 40px hsl(var(--gist-primary) / 0.2);
  --shadow-card: 0 10px 30px -10px hsl(0 0% 0% / 0.3);

  /* Sidebar colors */
  --sidebar-background: hsl(215 28% 9%);
  --sidebar-foreground: hsl(210 40% 90%);
  --sidebar-primary: hsl(238 84% 58%);
  --sidebar-primary-foreground: hsl(210 40% 98%);
  --sidebar-accent: hsl(217 19% 15%);
  --sidebar-accent-foreground: hsl(210 40% 90%);
  --sidebar-border: hsl(217 19% 25%);
  --sidebar-ring: hsl(238 84% 58%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

:root {
  --sidebar: hsl(215 28% 9%);
  --sidebar-foreground: hsl(210 40% 90%);
  --sidebar-primary: hsl(238 84% 58%);
  --sidebar-primary-foreground: hsl(210 40% 98%);
  --sidebar-accent: hsl(217 19% 15%);
  --sidebar-accent-foreground: hsl(210 40% 90%);
  --sidebar-border: hsl(217 19% 25%);
  --sidebar-ring: hsl(238 84% 58%);
}

.dark {
  --sidebar: hsl(215 28% 9%);
  --sidebar-foreground: hsl(210 40% 90%);
  --sidebar-primary: hsl(238 84% 58%);
  --sidebar-primary-foreground: hsl(210 40% 98%);
  --sidebar-accent: hsl(217 19% 15%);
  --sidebar-accent-foreground: hsl(210 40% 90%);
  --sidebar-border: hsl(217 19% 25%);
  --sidebar-ring: hsl(238 84% 58%);
}

@theme inline {
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
