"use client";

import { useAtomValue } from "jotai";
import {
  firebaseUserAtom,
  backendUser<PERSON>tom,
  authLoadingAtom,
} from "@/app/store/authAtom";
import { useRouter } from "next/navigation";
import { signOut } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { toast } from "sonner";
import { useEffect, Suspense } from "react";
import { Loader2, User, Database } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// A new component to handle data that needs the backend user profile
// This will suspend while the async backendUser<PERSON>tom is loading
function BackendUserProfile() {
  const backendUser = useAtomValue(backendUserAtom);
  const syncing = !backendUser; // A simple way to check if we're still syncing

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Backend User
          {syncing && <Loader2 className="h-4 w-4 animate-spin" />}
        </CardTitle>
        <CardDescription>Your synchronized database profile</CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        {backendUser ? (
          <>
            <div>
              <strong>ID:</strong> {backendUser.id}
            </div>
            <div>
              <strong>Email:</strong> {backendUser.email}
            </div>
            <div>
              <strong>Full Name:</strong> {backendUser.fullName || "Not set"}
            </div>
            <div>
              <strong>Created:</strong>{" "}
              {new Date(backendUser.createdAt).toLocaleDateString()}
            </div>
          </>
        ) : (
          <div className="text-muted-foreground">
            {syncing
              ? "Syncing with backend..."
              : "Failed to sync with backend"}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default function Dashboard() {
  // Read state directly from Jotai atoms instead of context
  const user = useAtomValue(firebaseUserAtom);
  const loading = useAtomValue(authLoadingAtom);
  const router = useRouter();

  // Redirect if not authenticated (this logic remains the same)
  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    }
  }, [user, loading, router]);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      toast.success("Signed out successfully");
      router.push("/login");
    } catch {
      toast.error("Failed to sign out");
    }
  };

  // The loading state now comes directly from our authLoadingAtom
  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return null; // Will be redirected to login by the useEffect
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Welcome to GistMind</p>
          </div>
          <Button onClick={handleSignOut} variant="outline">
            Sign Out
          </Button>
        </div>

        {/* User Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Firebase User Card (uses firebaseUserAtom) */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Firebase User
              </CardTitle>
              <CardDescription>
                Your Firebase authentication details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <strong>Email:</strong> {user.email}
              </div>
              <div>
                <strong>UID:</strong> {user.uid}
              </div>
            </CardContent>
          </Card>

          {/* Backend User Card (uses backendUserAtom via Suspense) */}
          <Suspense
            fallback={
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Backend User
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </CardTitle>
                  <CardDescription>
                    Your synchronized database profile
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-muted-foreground">
                  Syncing...
                </CardContent>
              </Card>
            }
          >
            <BackendUserProfile />
          </Suspense>
        </div>

        {/* Other sections like System Status and Quick Actions can be added here */}
      </div>
    </div>
  );
}
