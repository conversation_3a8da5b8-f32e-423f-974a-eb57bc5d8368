import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/sonner";
// import { AuthProvider } from "@/components/providers/auth-provider";
import "./globals.css"; // Your global styles
import { TooltipProvider } from "@/components/ui/tooltip";
import { Provider } from "jotai";
import { AuthInitializer } from "@/components/providers/auth-initializer";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Gist Mind",
  description: "Your intelligent content hub.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    // Enforce dark theme globally and apply the Inter font
    <html lang="en" className="dark">
      <body className={inter.className}>
        <TooltipProvider>
          <Provider>
            <AuthInitializer />
            {/* <AuthProvider> */}
            {children}
            <Toaster
              position="top-right"
              richColors
              closeButton
              theme="dark" // Ensure toasts match the dark UI
            />
            {/* </AuthProvider> */}
          </Provider>
        </TooltipProvider>
      </body>
    </html>
  );
}
