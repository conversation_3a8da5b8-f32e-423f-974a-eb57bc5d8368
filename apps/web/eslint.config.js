import { nextJsConfig } from "@repo/eslint-config/next-js";

/** @type {import("eslint").Linter.Config} */
const config = {
  // 1. First, spread all the properties from the shared config
  ...nextJsConfig,

  // 2. Then, add or override the 'rules' object
  rules: {
    // 3. Spread all the existing rules from the shared config to keep them
    ...nextJsConfig.rules,

    // 4. Add your new rule here to allow unused variables prefixed with _
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_"
      }
    ]
  }
};

export default config;