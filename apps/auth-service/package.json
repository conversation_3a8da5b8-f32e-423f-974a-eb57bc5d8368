{"name": "auth-service", "version": "1.0.0", "description": "Authentication microservice for GistMind", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["auth", "microservice", "firebase"], "devDependencies": {"@repo/typescript-config": "*", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^22.15.3", "tsx": "^4.19.2", "typescript": "^5.8.2"}, "dependencies": {"@repo/db": "*", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase-admin": "^13.0.1", "morgan": "^1.10.0", "zod": "^4.0.5"}, "author": "", "license": "ISC", "type": "commonjs"}