import express, { Request, Response } from "express";
import cors from "cors";
import morgan from "morgan";
import dotenv from "dotenv";
import { prisma } from "@repo/db";
import admin from "firebase-admin";
import { z } from "zod";

dotenv.config();

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  const serviceAccount = JSON.parse(
    Buffer.from(
      process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON!,
      "base64"
    ).toString("ascii")
  );
  admin.initializeApp({ credential: admin.credential.cert(serviceAccount) });
}

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3001",
    credentials: true,
  })
);
app.use(express.json());
app.use(morgan("dev"));

// Validation schemas
const syncUserSchema = z.object({
  fullName: z.string().optional(),
});

const updateUserSchema = z.object({
  fullName: z.string().min(1).max(100).optional(),
  email: z.string().email().optional(),
});

// Health check endpoint
app.get("/health", (_req: Request, res: Response) => {
  res.status(200).json({
    status: "healthy",
    service: "auth-service",
    timestamp: new Date().toISOString(),
  });
});

// Sync user from Firebase Auth to database
app.post("/users/sync", async (req: Request, res: Response) => {
  try {
    // The Firebase UID is passed from the gateway in the 'x-user-id' header
    const firebaseId = req.headers["x-user-id"] as string;
    const email = req.headers["x-user-email"] as string;

    if (!firebaseId || !email) {
      return res.status(400).json({
        error: "Missing user ID or email from gateway",
        code: "MISSING_USER_DATA",
      });
    }

    // Validate request body
    const validatedData = syncUserSchema.parse(req.body);

    // Upsert user in database
    const user = await prisma.user.upsert({
      where: { id: firebaseId },
      update: {
        // Update email if it changed in Firebase
        email,
        ...(validatedData.fullName && { fullName: validatedData.fullName }),
        updatedAt: new Date(),
      },
      create: {
        id: firebaseId,
        email,
        fullName: validatedData.fullName || null,
      },
    });

    console.log(`✅ User synced: ${user.email} (${user.id})`);

    res.status(200).json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    });
  } catch (error) {
    console.error("❌ Error syncing user:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid request data",
        code: "VALIDATION_ERROR",
        details: error.issues,
      });
    }

    res.status(500).json({
      error: "Failed to sync user profile",
      code: "SYNC_ERROR",
    });
  }
});

// Get user profile
app.get("/users/profile", async (req: Request, res: Response) => {
  try {
    const firebaseId = req.headers["x-user-id"] as string;

    if (!firebaseId) {
      return res.status(400).json({
        error: "Missing user ID from gateway",
        code: "MISSING_USER_ID",
      });
    }

    const user = await prisma.user.findUnique({
      where: { id: firebaseId },
      select: {
        id: true,
        email: true,
        fullName: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        error: "User not found",
        code: "USER_NOT_FOUND",
      });
    }

    res.status(200).json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("❌ Error fetching user profile:", error);
    res.status(500).json({
      error: "Failed to fetch user profile",
      code: "FETCH_ERROR",
    });
  }
});

// Update user profile
app.put("/users/profile", async (req: Request, res: Response) => {
  try {
    const firebaseId = req.headers["x-user-id"] as string;

    if (!firebaseId) {
      return res.status(400).json({
        error: "Missing user ID from gateway",
        code: "MISSING_USER_ID",
      });
    }

    // Validate request body
    const validatedData = updateUserSchema.parse(req.body);

    const user = await prisma.user.update({
      where: { id: firebaseId },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        email: true,
        fullName: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log(`✅ User updated: ${user.email} (${user.id})`);

    res.status(200).json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("❌ Error updating user profile:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid request data",
        code: "VALIDATION_ERROR",
        details: error.issues,
      });
    }

    res.status(500).json({
      error: "Failed to update user profile",
      code: "UPDATE_ERROR",
    });
  }
});

// Delete user account
app.delete("/users/profile", async (req: Request, res: Response) => {
  try {
    const firebaseId = req.headers["x-user-id"] as string;

    if (!firebaseId) {
      return res.status(400).json({
        error: "Missing user ID from gateway",
        code: "MISSING_USER_ID",
      });
    }

    // Delete user from database (cascade will handle related data)
    await prisma.user.delete({
      where: { id: firebaseId },
    });

    // Also delete from Firebase Auth
    try {
      await admin.auth().deleteUser(firebaseId);
      console.log(`✅ User deleted from Firebase: ${firebaseId}`);
    } catch (firebaseError) {
      console.warn(`⚠️ Failed to delete user from Firebase: ${firebaseError}`);
      // Continue anyway since database deletion succeeded
    }

    console.log(`✅ User account deleted: ${firebaseId}`);

    res.status(200).json({
      success: true,
      message: "User account deleted successfully",
    });
  } catch (error) {
    console.error("❌ Error deleting user account:", error);
    res.status(500).json({
      error: "Failed to delete user account",
      code: "DELETE_ERROR",
    });
  }
});

// Verify Firebase token (for debugging)
app.post("/auth/verify", async (req: Request, res: Response) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({
        error: "Missing ID token",
        code: "MISSING_TOKEN",
      });
    }

    const decodedToken = await admin.auth().verifyIdToken(idToken);

    res.status(200).json({
      success: true,
      user: {
        uid: decodedToken.uid,
        email: decodedToken.email,
        emailVerified: decodedToken.email_verified,
        name: decodedToken.name,
        picture: decodedToken.picture,
      },
    });
  } catch (error) {
    console.error("❌ Error verifying token:", error);
    res.status(401).json({
      error: "Invalid or expired token",
      code: "INVALID_TOKEN",
    });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Auth Service running on http://localhost:${PORT}`);
});
