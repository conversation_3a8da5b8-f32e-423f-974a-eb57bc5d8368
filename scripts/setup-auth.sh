#!/bin/bash

# GistMind Authentication Setup Script
echo "🚀 Setting up GistMind Authentication System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "apps" ]; then
    print_error "Please run this script from the root of your GistMind project"
    exit 1
fi

print_status "Installing dependencies..."

# Install root dependencies
npm install

# Install auth-service dependencies
print_status "Installing auth-service dependencies..."
cd apps/auth-service
npm install
cd ../..

# Install api-gateway dependencies
print_status "Installing api-gateway dependencies..."
cd apps/api-gateway
npm install
cd ../..

# Install web dependencies
print_status "Installing web dependencies..."
cd apps/web
npm install
cd ../..

# Install database dependencies
print_status "Installing database dependencies..."
cd packages/db
npm install
cd ../..

print_success "Dependencies installed successfully!"

# Setup environment files
print_status "Setting up environment files..."

if [ ! -f ".env" ]; then
    cp .env.example .env
    print_warning "Created .env file from .env.example. Please update with your actual values."
else
    print_warning ".env file already exists. Please ensure it has the required variables."
fi

if [ ! -f "apps/web/.env.local" ]; then
    cp apps/web/.env.example apps/web/.env.local
    print_warning "Created apps/web/.env.local file. Please update with your Firebase configuration."
else
    print_warning "apps/web/.env.local already exists. Please ensure it has the required Firebase variables."
fi

# Database setup
print_status "Setting up database..."
cd packages/db

# Generate Prisma client
npx prisma generate

print_warning "Database setup complete. Don't forget to:"
print_warning "1. Set up your PostgreSQL database"
print_warning "2. Update DATABASE_URL in .env"
print_warning "3. Run 'npx prisma db push' to create tables"

cd ../..

print_success "🎉 Authentication system setup complete!"
print_status "Next steps:"
echo "1. Update .env with your actual database URL and Firebase credentials"
echo "2. Update apps/web/.env.local with your Firebase frontend configuration"
echo "3. Set up your PostgreSQL database"
echo "4. Run 'npm run dev' to start all services"
echo ""
print_status "Available commands:"
echo "• npm run dev - Start all services in development mode"
echo "• cd apps/auth-service && npm run dev - Start only auth service"
echo "• cd apps/api-gateway && npm run dev - Start only API gateway"
echo "• cd apps/web && npm run dev - Start only frontend"
echo ""
print_status "Service URLs (default):"
echo "• Frontend: http://localhost:3001"
echo "• API Gateway: http://localhost:3000"
echo "• Auth Service: http://localhost:3002"
