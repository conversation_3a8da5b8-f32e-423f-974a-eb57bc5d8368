// schema.prisma
//
// This schema is designed for PostgreSQL and includes all new features.
// Place this in your shared `packages/database` directory in your Turborepo.

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // Loads from the .env file
}

// --- ENUMS ---
// Define reusable types for your models

enum ContentType {
  ARTICLE
  VIDEO
  AUDIO
  IMAGE
  PDF
  OTHER
}

enum SummaryType {
  QUICK
  DETAILED
}

enum SummaryStatus {
  PENDING
  COMPLETED
  FAILED
}

// --- AUTH & USER MODEL ---
// This model stores user data. The ID is the unique UID from Firebase Auth.

model User {
  // The unique ID from Firebase Auth. This is the primary key.
  id String @id

  email    String  @unique
  fullName String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // --- Relationships ---
  contents   Content[]
  tags       Tag[]
  folders    Folder[]
  summaries  Summary[]
  highlights Highlight[]
  apiKeys    ApiKey[]
  webhooks   Webhook[]
  shareLinks ShareLink[]
}

// --- CONTENT ORGANIZATION MODELS ---
// These models handle how content is stored and organized.

model Content {
  id String @id @default(cuid())

  link      String
  title     String
  type      ContentType @default(ARTICLE)
  fullText  String?     @db.Text // Stores the scraped article text
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  // Foreign keys for relationships
  userId   String
  folderId String?

  // --- Relationships ---
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  folder Folder? @relation(fields: [folderId], references: [id], onDelete: SetNull)

  tags       Tag[]
  summary    Summary[]
  highlights Highlight[]
  shareLinks ShareLink[]

  @@index([userId])
  @@index([folderId])
}

model Tag {
  id String @id @default(cuid())

  title     String
  createdAt DateTime @default(now())

  // Foreign key for the user who created the tag
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Many-to-many relationship with Content
  contents Content[]

  // A tag's title must be unique for a given user
  @@unique([userId, title])
}

model Folder {
  id String @id @default(cuid())

  name      String
  createdAt DateTime @default(now())

  // Foreign key for the user who created the folder
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // --- Self-relation for nested folders ---
  parentId String?
  parent   Folder?  @relation("NestedFolders", fields: [parentId], references: [id], onDelete: SetNull)
  children Folder[] @relation("NestedFolders")

  // One-to-many relationship with Content
  contents Content[]

  @@index([userId])
}

// --- NEW FEATURE: HIGHLIGHTS & ANNOTATIONS ---

model Highlight {
  id String @id @default(cuid())

  quote     String   @db.Text // The highlighted text
  note      String?  @db.Text // The user's personal annotation/note
  createdAt DateTime @default(now())

  // Foreign keys
  userId    String
  contentId String

  // --- Relationships ---
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  content Content @relation(fields: [contentId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([contentId])
}

// --- AI & SHARING MODELS ---

model Summary {
  id String @id @default(cuid())

  type           SummaryType
  status         SummaryStatus @default(PENDING)
  summaryText    String?       @db.Text
  errorMessage   String?
  tokensUsed     Int?
  processingTime Int?          // in milliseconds
  modelUsed      String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign keys
  userId    String
  contentId String

  // --- Relationships ---
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  content Content @relation(fields: [contentId], references: [id], onDelete: Cascade)

  // A piece of content can only have one summary of each type (e.g., one 'quick', one 'detailed')
  @@unique([contentId, type])
  @@index([userId])
}

model ShareLink {
  id String @id @default(cuid())

  shortCode       String   @unique @default(cuid())
  isActive        Boolean  @default(true)
  expiresAt       DateTime?
  maxAccesses     Int?
  currentAccesses Int      @default(0)
  createdAt       DateTime @default(now())

  // Foreign keys
  userId    String
  contentId String

  // --- Relationships ---
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  content Content @relation(fields: [contentId], references: [id], onDelete: Cascade)
}

// --- ADVANCED PLATFORM FEATURES ---

model ApiKey {
  id String @id @default(cuid())

  hashedKey String   @unique // Store a hash of the key, not the key itself
  name      String   // A user-friendly name for the key
  createdAt DateTime @default(now())
  expiresAt DateTime?

  // Foreign key
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Webhook {
  id String @id @default(cuid())

  targetUrl String   // The URL to send the event payload to
  event     String   // The event to subscribe to (e.g., "content.created")
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  // Foreign key
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}
