# docker-compose.yml
#
# Use this file to run PostgreSQL, Redis, and RabbitMQ containers locally.
# Run `docker-compose up -d` in your terminal to start all services.
#
version: '3.8'

services:
  # PostgreSQL Database Service
  postgres:
    image: postgres:17-alpine
    container_name: gistmind-postgres
    restart: always
    environment:
      POSTGRES_USER: user        # Change this
      POSTGRES_PASSWORD: password  # Change this
      POSTGRES_DB: gistmind      # Change this
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d gistmind"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Caching Service
  redis:
    image: redis:8-alpine
    container_name: gistmind-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ Message Broker Service
  rabbitmq:
    image: rabbitmq:4-management-alpine
    container_name: gistmind-rabbitmq
    restart: always
    ports:
      - "5672:5672"   # Port for application connections
      - "15672:15672" # Port for the web management UI
    environment:
      RABBITMQS_DEFAULT_USER: user        # Change this
      RABBITMQS_DEFAULT_PASS: password  # Change this
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq/

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data: